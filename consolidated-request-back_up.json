{"operationType": "CROSS_SELL_STREAM | CROSS_SELL_V2_STREAM | FETCH_COLLECTIONS | LANDING_DISCOVERY | MOB_LANDING", "apiVersion": "v1 | v2", "correlationKey": "uuid-string", "brand": "MMT | GOIBIBO", "context": {"contextId": "string", "experimentData": "string", "pageContext": "HOTEL_LANDING | HOTEL_SEARCH | MOBILE_LANDING", "scope": "string", "funnelSource": "string"}, "requestDetails": {"requestor": "SCION", "requestId": "string", "channel": "MOBILE | WEB | APP", "region": "IN | US | UK", "profile": "PERSONAL | BUSINESS", "visitorId": "string", "loggedIn": false, "visitNumber": 1, "premium": false}, "deviceDetails": {"deviceId": "string", "deviceType": "ANDROID | IOS | WEB", "bookingDevice": "MOBILE | DESKTOP | TABLET", "appVersion": "string", "platform": "ANDROID | IOS", "model": "string"}, "user": {"mmtAuth": "string", "uuid": "string", "profileType": "PERSONAL | BUSINESS", "visitorId": "string", "mcid": "string", "state": "string", "location": {"locationData": {"currentLocation": {"latitude": 19.076, "longitude": 72.8777}}}}, "searchCriteria": {"hotelId": "csckds", "checkIn": "2024-01-15", "checkOut": "2024-01-17", "countryCode": "IN", "locationId": "CTBOM", "locationType": "city", "currency": "INR", "limit": 10, "personalizedSearch": false, "nearBySearch": false, "language": "eng", "timestamp": 1705276800, "roomStayCandidates": [{"adultCount": 2, "rooms": 1, "childAges": []}]}, "searchEvents": [{"cardId": "HOTEL_CROSS_SELL | HOTEL_XSELL_BENEFITS | HANDPICKED_PROPERTIES | VALUESTAYS | DAILYSTEALDEAL", "templateId": "string", "componentId": "string", "limit": 10, "hydraSegIds": ["string"], "selectedTabId": "string", "isLandingDiscoveryPage": false, "persuasionRequired": false, "filter": false, "appliedFilterMap": {}, "funnelActivity": {}, "sc": {"lob": "string", "lobCategory": "string", "fromDateTime": {"str": "2024-01-15", "ts": 1705276800, "zone": "Asia/Kolkata"}, "toDateTime": {"str": "2024-01-17", "ts": 1705449600, "zone": "Asia/Kolkata"}, "pax": [{"count": 2, "details": {"adult": {"ages": [25, 30], "count": 2}, "child": {"ages": [], "count": 0}, "infant": {"ages": [], "count": 0}}}], "from": {"lobCity": "string", "locus": {"city": "string", "areaId": "string", "areaName": "string", "poiId": "string", "poiName": "string", "type": "city", "id": "CTBOM", "cityName": "Mumbai"}, "cityName": "Mumbai", "countryName": "India", "countryCode": "IN", "longitude": 72.8777, "latitude": 19.076}, "to": {"lobCity": "string", "locus": {"city": "string", "areaId": "string", "areaName": "string", "poiId": "string", "poiName": "string", "type": "city", "id": "CTDEL", "cityName": "Delhi"}, "cityName": "Delhi", "countryName": "India", "countryCode": "IN", "longitude": 77.209, "latitude": 28.6139}, "timestamp": 1705276800, "rooms": 1, "funnelSource": "string", "personalizedSearch": false, "product": {"id": "string", "name": "string"}}, "enrichments": {"trends": {"areas": [], "price_bucket": [], "ap_bucket": [], "los_bucket": [], "pax": [], "pd_id": [], "star_rating": [], "accommodation_type": [], "cities": []}, "userPreferences": {}}, "tags": {"edge_type": "string", "source": "string"}, "filterList": [], "recentlyViewedHotels": [], "recommendedPlaces": [], "meta": {"gd": "string"}}], "cards": [{"cardId": "string", "cardVariantId": "string", "templateId": "string", "componentId": "string", "searchEvent": {"sc": {"lob": "string", "lobCategory": "string", "fromDateTime": {"str": "2024-01-15", "ts": 1705276800, "zone": "Asia/Kolkata"}, "toDateTime": {"str": "2024-01-17", "ts": 1705449600, "zone": "Asia/Kolkata"}, "pax": [{"count": 2, "details": {"adult": {"ages": [25, 30], "count": 2}, "child": {"ages": [], "count": 0}, "infant": {"ages": [], "count": 0}}}], "from": {"lobCity": "string", "locus": {"city": "string", "areaId": "string", "areaName": "string", "poiId": "string", "poiName": "string", "type": "city", "id": "CTBOM", "cityName": "Mumbai"}, "cityName": "Mumbai", "countryName": "India", "countryCode": "IN", "longitude": 72.8777, "latitude": 19.076}, "to": {"lobCity": "string", "locus": {"city": "string", "areaId": "string", "areaName": "string", "poiId": "string", "poiName": "string", "type": "city", "id": "CTDEL", "cityName": "Delhi"}, "cityName": "Delhi", "countryName": "India", "countryCode": "IN", "longitude": 77.209, "latitude": 28.6139}, "timestamp": 1705276800, "rooms": 1, "funnelSource": "string", "personalizedSearch": false, "product": {"id": "string", "name": "string"}}}, "data": {"cardList": [{"description": "string", "subDescription": "string", "imageUrl": "string", "deepLink": "string", "actionText": "string"}], "cardInfo": {"index": 0, "starText": "string", "cardId": "string", "titleText": "string", "subText": "string", "cardAction": [{"webViewUrl": "string", "title": "string"}], "cardPayload": {"genericCardData": [{"titleText": "string", "iconUrl": "string"}]}, "iconUrl": "string", "bgImageUrl": "string", "templateId": "string"}, "hotelList": [{"id": "string", "name": "string", "propertyType": "HOTEL", "mainImages": ["string"], "starRating": 5, "currencyCode": "INR", "displayFare": 15000.0, "cityName": "string", "desktopDeeplink": "string", "appDeepLink": "string", "locationPersuasion": ["string"], "freeCancellationText": "string", "inclusions": ["string"], "cancellationTimeline": "string", "flyfishReviewSummary": {"rating": 4.5, "reviewCount": 1250}}], "appliedFilterMap": {"PRICE_RANGE": [{"filterGroup": "PRICE_RANGE", "filterValue": "string", "title": "string", "rangeFilter": true}]}, "bgImageUrl": "string", "offerPersuasions": [{"text": "string", "imageUrl": "string", "expiryTimestamp": 1705276800}]}, "headerData": {"header": "string", "subheader": "string", "lobSubheader": "string", "cta": {"title": "string", "deeplink": "string"}}}], "featureFlags": {"locus": false, "coupon": false, "bestCoupon": false}, "expData": "string", "metaInfo": {"userName": "string", "bookingId": "string"}}